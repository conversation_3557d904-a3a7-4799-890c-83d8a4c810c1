export class Router {
  constructor() {
    this.routes = new Map();
    this.currentRoute = null;
    this.beforeRouteChange = null;
    this.afterRouteChange = null;
  }

  addRoute(path, handler, options = {}) {
    this.routes.set(path, {
      handler,
      requiresAuth: options.requiresAuth || false,
      roles: options.roles || [],
      title: options.title || 'WaaMan'
    });
  }

  navigate(path, replace = false) {
    if (replace) {
      history.replaceState(null, '', path);
    } else {
      history.pushState(null, '', path);
    }
    this.handleRoute();
  }

  async handleRoute() {
    const path = window.location.pathname;
    const route = this.routes.get(path);

    console.log('Router handling route:', path, route);

    // Call before route change hook
    if (this.beforeRouteChange) {
      const shouldContinue = await this.beforeRouteChange(path, this.currentRoute);
      if (!shouldContinue) {
        return;
      }
    }

    if (route) {
      // Check authentication requirements
      if (route.requiresAuth && !this.isAuthenticated()) {
        console.log('Route requires auth, redirecting to login');
        this.navigate('/login');
        return;
      }

      // Check role requirements
      if (route.roles.length > 0 && !this.hasRequiredRole(route.roles)) {
        console.log('User does not have required role');
        this.showUnauthorized();
        return;
      }

      try {
        // Update page title
        document.title = route.title;

        // Execute route handler
        console.log('Executing route handler for:', path);
        await route.handler();

        this.currentRoute = path;

        // Call after route change hook
        if (this.afterRouteChange) {
          this.afterRouteChange(path);
        }

      } catch (error) {
        console.error('Route handler error:', error);
        this.showError('Failed to load page');
      }
    } else {
      console.log('No route found for:', path);
      this.show404();
    }
  }

  isAuthenticated() {
    const token = localStorage.getItem('waaman_token');
    return !!token;
  }

  hasRequiredRole(requiredRoles) {
    const user = JSON.parse(localStorage.getItem('waaman_user') || '{}');
    return requiredRoles.includes(user.role);
  }

  show404() {
    const app = document.getElementById('app');
    app.innerHTML = `
      <div class="min-h-screen flex items-center justify-center bg-gray-50">
        <div class="max-w-md w-full bg-white rounded-lg shadow-md p-6 text-center">
          <div class="text-gray-400 text-6xl mb-4">
            <i class="fas fa-map-marker-slash"></i>
          </div>
          <h2 class="text-2xl font-bold text-gray-900 mb-2">Page Not Found</h2>
          <p class="text-gray-600 mb-6">The page you're looking for doesn't exist.</p>
          <button onclick="history.back()" 
                  class="btn-primary mr-2">
            Go Back
          </button>
          <button data-route="/" 
                  class="btn-outline">
            Go Home
          </button>
        </div>
      </div>
    `;
  }

  showUnauthorized() {
    const app = document.getElementById('app');
    app.innerHTML = `
      <div class="min-h-screen flex items-center justify-center bg-gray-50">
        <div class="max-w-md w-full bg-white rounded-lg shadow-md p-6 text-center">
          <div class="text-red-400 text-6xl mb-4">
            <i class="fas fa-shield-alt"></i>
          </div>
          <h2 class="text-2xl font-bold text-gray-900 mb-2">Access Denied</h2>
          <p class="text-gray-600 mb-6">You don't have permission to access this page.</p>
          <button onclick="history.back()" 
                  class="btn-primary mr-2">
            Go Back
          </button>
          <button data-route="/dashboard" 
                  class="btn-outline">
            Dashboard
          </button>
        </div>
      </div>
    `;
  }

  showError(message) {
    const app = document.getElementById('app');
    app.innerHTML = `
      <div class="min-h-screen flex items-center justify-center bg-gray-50">
        <div class="max-w-md w-full bg-white rounded-lg shadow-md p-6 text-center">
          <div class="text-red-500 text-6xl mb-4">
            <i class="fas fa-exclamation-triangle"></i>
          </div>
          <h2 class="text-xl font-semibold text-gray-900 mb-2">Error</h2>
          <p class="text-gray-600 mb-4">${message}</p>
          <button onclick="window.location.reload()" 
                  class="btn-primary mr-2">
            Refresh
          </button>
          <button onclick="history.back()" 
                  class="btn-outline">
            Go Back
          </button>
        </div>
      </div>
    `;
  }

  // Route guards
  setBeforeRouteChange(callback) {
    this.beforeRouteChange = callback;
  }

  setAfterRouteChange(callback) {
    this.afterRouteChange = callback;
  }

  // Get current route
  getCurrentRoute() {
    return this.currentRoute;
  }

  // Get route parameters (for future parameterized routes)
  getRouteParams(path) {
    // Simple implementation - can be extended for complex routing
    const urlParams = new URLSearchParams(window.location.search);
    const params = {};
    for (const [key, value] of urlParams) {
      params[key] = value;
    }
    return params;
  }

  // Redirect helper
  redirect(path) {
    this.navigate(path, true);
  }

  // Back navigation
  back() {
    history.back();
  }

  // Forward navigation
  forward() {
    history.forward();
  }
}
