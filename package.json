{"name": "waaman-waste-management", "version": "1.0.0", "description": "Comprehensive waste management system with real-time tracking and multi-module architecture", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server:dev\" \"npm run client:dev\"", "server:dev": "nodemon server/index.js", "client:dev": "cd client && npm run dev", "build": "cd client && npm run build", "start": "node server/index.js", "seed": "node scripts/seed-database.js", "test": "jest", "test:watch": "jest --watch"}, "keywords": ["waste-management", "tracking", "municipal", "real-time", "nodejs", "express", "firebase"], "author": "WaaMan Development Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "firebase-admin": "^11.11.1", "socket.io": "^4.7.4", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "nodemailer": "^6.9.7", "multer": "^1.4.5-lts.1", "compression": "^1.7.4", "rate-limiter-flexible": "^2.4.2", "joi": "^17.11.0", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.2", "concurrently": "^8.2.2", "jest": "^29.7.0", "supertest": "^6.3.3", "@types/jest": "^29.5.8"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}