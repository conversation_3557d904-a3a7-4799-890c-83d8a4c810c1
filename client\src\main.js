import './styles/main.css';
import { AuthService } from './services/auth.js';
import { Router } from './utils/router.js';
import { NotificationService } from './services/notifications.js';
import { SocketService } from './services/socket.js';

class WaaManApp {
  constructor() {
    this.authService = new AuthService();
    this.router = new Router();
    this.notificationService = new NotificationService();
    this.socketService = new SocketService();
    
    this.init();
  }

  async init() {
    try {
      // Hide loading spinner
      this.hideLoading();
      
      // Initialize services
      await this.initializeServices();
      
      // Set up routing
      this.setupRouting();
      
      // Set up global event listeners
      this.setupEventListeners();
      
      // Start the application
      this.start();
      
    } catch (error) {
      console.error('Failed to initialize WaaMan app:', error);
      this.showError('Failed to initialize application. Please refresh the page.');
    }
  }

  async initializeServices() {
    // Initialize notification service
    await this.notificationService.init();

    // Check if user is already logged in
    const token = localStorage.getItem('waaman_token');
    if (token) {
      try {
        const user = await this.authService.getCurrentUser();
        if (user && user.id) {
          console.log('User already logged in:', user);
          // Initialize socket connection for authenticated users
          this.socketService.connect(token);
        } else {
          // Invalid user data, clear storage
          localStorage.removeItem('waaman_token');
          localStorage.removeItem('waaman_user');
        }
      } catch (error) {
        console.error('Error checking authentication:', error);
        // Token might be expired, clear it
        localStorage.removeItem('waaman_token');
        localStorage.removeItem('waaman_user');
      }
    }
  }

  setupRouting() {
    // Define routes
    this.router.addRoute('/', () => this.showHomePage());
    this.router.addRoute('/login', () => this.showLoginPage());
    this.router.addRoute('/register', () => this.showRegisterPage());
    this.router.addRoute('/dashboard', () => this.showDashboard());
    this.router.addRoute('/tracking', () => this.showTrackingPage());
    this.router.addRoute('/complaints', () => this.showComplaintsPage());
    this.router.addRoute('/profile', () => this.showProfilePage());
    
    // Admin routes
    this.router.addRoute('/admin', () => this.showAdminDashboard());
    this.router.addRoute('/admin/users', () => this.showUserManagement());
    this.router.addRoute('/admin/vehicles', () => this.showVehicleManagement());
    this.router.addRoute('/admin/complaints', () => this.showComplaintManagement());
    
    // Driver/Worker routes
    this.router.addRoute('/driver', () => this.showDriverDashboard());
    this.router.addRoute('/driver/vehicle', () => this.showVehicleControl());
    this.router.addRoute('/driver/emergency', () => this.showEmergencyPage());
  }

  setupEventListeners() {
    // Handle authentication state changes
    document.addEventListener('auth:login', (event) => {
      const { user, token } = event.detail;
      console.log('Auth login event received:', user);

      if (user && user.role) {
        this.socketService.connect(token);

        // Navigate to role-specific dashboard
        switch (user.role) {
          case 'admin':
            this.router.navigate('/admin');
            break;
          case 'driver':
          case 'worker':
            this.router.navigate('/driver');
            break;
          case 'user':
          default:
            this.router.navigate('/dashboard');
            break;
        }
      } else {
        console.error('Invalid user data in login event:', user);
        this.router.navigate('/dashboard');
      }
    });

    document.addEventListener('auth:logout', () => {
      this.socketService.disconnect();
      this.router.navigate('/');
    });

    // Handle navigation
    document.addEventListener('click', (event) => {
      const link = event.target.closest('[data-route]');
      if (link) {
        event.preventDefault();
        const route = link.getAttribute('data-route');
        this.router.navigate(route);
      }
    });

    // Handle back/forward browser buttons
    window.addEventListener('popstate', () => {
      this.router.handleRoute();
    });
  }

  start() {
    // Handle initial route
    this.router.handleRoute();
  }

  hideLoading() {
    const loading = document.getElementById('loading');
    if (loading) {
      loading.style.display = 'none';
    }
  }

  showError(message) {
    const app = document.getElementById('app');
    app.innerHTML = `
      <div class="min-h-screen flex items-center justify-center bg-gray-50">
        <div class="max-w-md w-full bg-white rounded-lg shadow-md p-6 text-center">
          <div class="text-red-500 text-6xl mb-4">
            <i class="fas fa-exclamation-triangle"></i>
          </div>
          <h2 class="text-xl font-semibold text-gray-900 mb-2">Application Error</h2>
          <p class="text-gray-600 mb-4">${message}</p>
          <button onclick="window.location.reload()" 
                  class="bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700 transition-colors">
            Refresh Page
          </button>
        </div>
      </div>
    `;
  }

  // Page rendering methods
  showHomePage() {
    import('./modules/home/<USER>').then(module => {
      new module.HomePage().render();
    });
  }

  showLoginPage() {
    import('./modules/auth/LoginPage.js').then(module => {
      new module.LoginPage().render();
    });
  }

  showRegisterPage() {
    import('./modules/auth/RegisterPage.js').then(module => {
      new module.RegisterPage().render();
    });
  }

  async showDashboard() {
    const user = await this.authService.getCurrentUser();
    console.log('Dashboard - Current user:', user);

    if (!user || !user.id) {
      console.log('No valid user found, redirecting to login');
      this.router.navigate('/login');
      return;
    }

    // Route to appropriate dashboard based on user role
    console.log('Routing to dashboard for role:', user.role);
    switch (user.role) {
      case 'admin':
        this.showAdminDashboard();
        break;
      case 'driver':
      case 'worker':
        this.showDriverDashboard();
        break;
      case 'user':
      default:
        this.showUserDashboard();
        break;
    }
  }

  showUserDashboard() {
    import('./modules/user/UserDashboard.js').then(module => {
      new module.UserDashboard().render();
    });
  }

  showAdminDashboard() {
    import('./modules/admin/AdminDashboard.js').then(module => {
      new module.AdminDashboard().render();
    });
  }

  showDriverDashboard() {
    import('./modules/driver/DriverDashboard.js').then(module => {
      new module.DriverDashboard().render();
    });
  }

  showTrackingPage() {
    import('./modules/tracking/TrackingPage.js').then(module => {
      new module.TrackingPage().render();
    });
  }

  showComplaintsPage() {
    import('./modules/complaints/ComplaintsPage.js').then(module => {
      new module.ComplaintsPage().render();
    });
  }

  showProfilePage() {
    import('./modules/profile/ProfilePage.js').then(module => {
      new module.ProfilePage().render();
    });
  }

  showUserManagement() {
    import('./modules/admin/UserManagement.js').then(module => {
      new module.UserManagement().render();
    });
  }

  showVehicleManagement() {
    import('./modules/admin/VehicleManagement.js').then(module => {
      new module.VehicleManagement().render();
    });
  }

  showComplaintManagement() {
    import('./modules/admin/ComplaintManagement.js').then(module => {
      new module.ComplaintManagement().render();
    });
  }

  showVehicleControl() {
    import('./modules/driver/VehicleControl.js').then(module => {
      new module.VehicleControl().render();
    });
  }

  showEmergencyPage() {
    import('./modules/driver/EmergencyPage.js').then(module => {
      new module.EmergencyPage().render();
    });
  }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new WaaManApp();
});
