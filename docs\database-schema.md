# WaaMan Database Schema Design

## Firebase Realtime Database Structure

```
waaman-database/
├── users/
│   └── {userId}/
│       ├── id: string
│       ├── email: string
│       ├── firstName: string
│       ├── lastName: string
│       ├── phone: string (optional)
│       ├── role: "user" | "driver" | "worker" | "admin"
│       ├── status: "active" | "inactive" | "deleted"
│       ├── profile/
│       │   ├── avatar: string (URL)
│       │   └── preferences/
│       │       └── notifications/
│       │           ├── email: boolean
│       │           ├── push: boolean
│       │           └── sms: boolean
│       ├── createdAt: timestamp
│       ├── updatedAt: timestamp
│       ├── lastLogin: timestamp
│       └── lastLogout: timestamp
│
├── vehicles/
│   └── {vehicleId}/
│       ├── id: string
│       ├── plateNumber: string
│       ├── type: "truck" | "compactor" | "pickup"
│       ├── capacity: number (in tons)
│       ├── model: string
│       ├── year: number
│       ├── assignedTo: string (userId)
│       ├── route: string (routeId)
│       ├── status: "active" | "maintenance" | "inactive"
│       ├── location/
│       │   ├── latitude: number
│       │   ├── longitude: number
│       │   ├── speed: number
│       │   ├── heading: number
│       │   └── lastUpdated: timestamp
│       ├── maintenance/
│       │   ├── lastService: timestamp
│       │   ├── nextService: timestamp
│       │   └── mileage: number
│       ├── fuel/
│       │   ├── level: number (percentage)
│       │   ├── lastRefill: timestamp
│       │   └── consumption: array of consumption records
│       ├── createdAt: timestamp
│       └── updatedAt: timestamp
│
├── complaints/
│   └── {complaintId}/
│       ├── id: string
│       ├── title: string
│       ├── description: string
│       ├── category: "missed_collection" | "damaged_bin" | "spillage" | "noise" | "other"
│       ├── priority: "low" | "medium" | "high" | "urgent"
│       ├── status: "pending" | "in_progress" | "resolved" | "closed"
│       ├── userId: string (optional for anonymous)
│       ├── userInfo/
│       │   ├── name: string
│       │   ├── email: string
│       │   └── phone: string
│       ├── location/
│       │   ├── latitude: number
│       │   ├── longitude: number
│       │   └── address: string
│       ├── attachments: array of file objects
│       ├── assignedTo: string (userId)
│       ├── anonymous: boolean
│       ├── timeline: array of status change records
│       ├── comments: array of comment objects
│       ├── createdAt: timestamp
│       └── updatedAt: timestamp
│
├── routes/
│   └── {routeId}/
│       ├── id: string
│       ├── name: string
│       ├── description: string
│       ├── waypoints: array of coordinate objects
│       ├── estimatedDuration: number (minutes)
│       ├── area: string
│       ├── status: "active" | "inactive"
│       ├── assignedVehicles: array of vehicleIds
│       ├── createdAt: timestamp
│       └── updatedAt: timestamp
│
├── location_history/
│   └── {historyId}/
│       ├── id: string
│       ├── vehicleId: string
│       ├── latitude: number
│       ├── longitude: number
│       ├── speed: number
│       ├── heading: number
│       ├── timestamp: timestamp
│       └── updatedBy: string (userId)
│
├── emergencies/
│   └── {emergencyId}/
│       ├── id: string
│       ├── userId: string
│       ├── userName: string
│       ├── userRole: string
│       ├── type: "general" | "accident" | "breakdown" | "medical"
│       ├── message: string
│       ├── location/
│       │   ├── latitude: number
│       │   └── longitude: number
│       ├── status: "active" | "resolved"
│       ├── timestamp: timestamp
│       └── resolvedAt: timestamp
│
├── notifications/
│   └── {notificationId}/
│       ├── id: string
│       ├── userId: string
│       ├── type: "vehicle_approaching" | "complaint_update" | "emergency" | "system"
│       ├── title: string
│       ├── message: string
│       ├── data: object (additional data)
│       ├── read: boolean
│       ├── createdAt: timestamp
│       └── readAt: timestamp
│
└── system_settings/
    ├── app_version: string
    ├── maintenance_mode: boolean
    ├── default_settings/
    │   ├── notification_radius: number (km)
    │   ├── vehicle_update_interval: number (seconds)
    │   └── complaint_auto_close_days: number
    └── updated_at: timestamp
```

## Data Relationships

### User Roles and Permissions
- **User**: Can view vehicles, submit complaints, manage profile
- **Driver/Worker**: Can update vehicle location, view assigned routes, send emergency alerts
- **Admin**: Full access to all data, user management, system settings

### Key Relationships
- Users ↔ Vehicles (assignment)
- Vehicles ↔ Routes (assignment)
- Users ↔ Complaints (ownership/assignment)
- Vehicles ↔ Location History (tracking)
- Users ↔ Emergencies (reporting)
- Users ↔ Notifications (delivery)

## Indexes for Performance

### Recommended Firebase Indexes
```json
{
  "rules": {
    "users": {
      ".indexOn": ["email", "role", "status", "createdAt"]
    },
    "vehicles": {
      ".indexOn": ["assignedTo", "status", "type", "plateNumber"]
    },
    "complaints": {
      ".indexOn": ["userId", "status", "category", "priority", "createdAt"]
    },
    "location_history": {
      ".indexOn": ["vehicleId", "timestamp"]
    },
    "notifications": {
      ".indexOn": ["userId", "read", "createdAt"]
    }
  }
}
```

## Sample Data Structure

### Sample User
```json
{
  "id": "user_123",
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe",
  "phone": "+1234567890",
  "role": "user",
  "status": "active",
  "profile": {
    "avatar": null,
    "preferences": {
      "notifications": {
        "email": true,
        "push": true,
        "sms": false
      }
    }
  },
  "createdAt": 1704067200000,
  "updatedAt": 1704067200000
}
```

### Sample Vehicle
```json
{
  "id": "vehicle_456",
  "plateNumber": "WM-001",
  "type": "compactor",
  "capacity": 15,
  "model": "Volvo FE",
  "year": 2022,
  "assignedTo": "driver_789",
  "route": "route_101",
  "status": "active",
  "location": {
    "latitude": 40.7128,
    "longitude": -74.0060,
    "speed": 25,
    "heading": 180,
    "lastUpdated": 1704067200000
  },
  "maintenance": {
    "lastService": 1703462400000,
    "nextService": 1706140800000,
    "mileage": 45000
  },
  "fuel": {
    "level": 75,
    "lastRefill": 1704067200000,
    "consumption": []
  },
  "createdAt": 1704067200000,
  "updatedAt": 1704067200000
}
```

### Sample Complaint
```json
{
  "id": "complaint_789",
  "title": "Missed Collection",
  "description": "Garbage was not collected on scheduled day",
  "category": "missed_collection",
  "priority": "medium",
  "status": "pending",
  "userId": "user_123",
  "userInfo": {
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+1234567890"
  },
  "location": {
    "latitude": 40.7128,
    "longitude": -74.0060,
    "address": "123 Main St, New York, NY"
  },
  "attachments": [],
  "assignedTo": null,
  "anonymous": false,
  "timeline": [
    {
      "action": "created",
      "timestamp": 1704067200000,
      "note": "Complaint submitted"
    }
  ],
  "comments": [],
  "createdAt": 1704067200000,
  "updatedAt": 1704067200000
}
```
