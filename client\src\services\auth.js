import { ApiService } from './api.js';

export class AuthService {
  constructor() {
    this.apiService = new ApiService();
    this.currentUser = null;
    this.token = localStorage.getItem('waaman_token');
    
    // Set token in API service if it exists
    if (this.token) {
      this.apiService.setAuthToken(this.token);
    }
  }

  async login(email, password) {
    try {
      const response = await this.apiService.post('/auth/login', {
        email,
        password
      });

      if (response.success) {
        const { user, token } = response.data;

        console.log('Login successful, user data:', user);

        // Store token and user data
        this.token = token;
        this.currentUser = user;
        localStorage.setItem('waaman_token', token);
        localStorage.setItem('waaman_user', JSON.stringify(user));

        // Set token in API service
        this.apiService.setAuthToken(token);

        // Dispatch login event
        console.log('Dispatching auth:login event with user:', user);
        document.dispatchEvent(new CustomEvent('auth:login', {
          detail: { user, token }
        }));

        return { success: true, user, token };
      }
      
      return { success: false, error: response.error };
    } catch (error) {
      console.error('Login error:', error);
      return { 
        success: false, 
        error: error.message || 'Login failed. Please try again.' 
      };
    }
  }

  async register(userData) {
    try {
      const response = await this.apiService.post('/auth/register', userData);

      if (response.success) {
        const { user, token } = response.data;

        console.log('Registration successful, user data:', user);

        // Store token and user data
        this.token = token;
        this.currentUser = user;
        localStorage.setItem('waaman_token', token);
        localStorage.setItem('waaman_user', JSON.stringify(user));

        // Set token in API service
        this.apiService.setAuthToken(token);

        // Dispatch login event
        console.log('Dispatching auth:login event with user:', user);
        document.dispatchEvent(new CustomEvent('auth:login', {
          detail: { user, token }
        }));

        return { success: true, user, token };
      }
      
      return { success: false, error: response.error };
    } catch (error) {
      console.error('Registration error:', error);
      return { 
        success: false, 
        error: error.message || 'Registration failed. Please try again.' 
      };
    }
  }

  async logout() {
    try {
      // Call logout endpoint if user is authenticated
      if (this.token) {
        await this.apiService.post('/auth/logout');
      }
    } catch (error) {
      console.error('Logout API error:', error);
      // Continue with local logout even if API call fails
    }

    // Clear local data
    this.token = null;
    this.currentUser = null;
    localStorage.removeItem('waaman_token');
    localStorage.removeItem('waaman_user');
    
    // Clear token from API service
    this.apiService.setAuthToken(null);
    
    // Dispatch logout event
    document.dispatchEvent(new CustomEvent('auth:logout'));
    
    return { success: true };
  }

  async getCurrentUser() {
    // Return cached user if available
    if (this.currentUser) {
      return this.currentUser;
    }

    // Try to get user from localStorage
    const storedUser = localStorage.getItem('waaman_user');
    if (storedUser && storedUser !== 'undefined') {
      try {
        this.currentUser = JSON.parse(storedUser);
        if (this.currentUser && this.currentUser.id) {
          return this.currentUser;
        }
      } catch (error) {
        console.error('Error parsing stored user:', error);
        localStorage.removeItem('waaman_user');
      }
    }

    // If we have a token, try to fetch user profile
    if (this.token) {
      try {
        const response = await this.apiService.get('/auth/profile');
        if (response.success && response.data && response.data.user) {
          this.currentUser = response.data.user;
          localStorage.setItem('waaman_user', JSON.stringify(this.currentUser));
          return this.currentUser;
        }
      } catch (error) {
        console.error('Error fetching user profile:', error);
        // Token might be invalid, clear it
        await this.logout();
      }
    }

    return null;
  }

  async updateProfile(profileData) {
    try {
      const response = await this.apiService.put('/auth/profile', profileData);

      if (response.success) {
        this.currentUser = response.data.user;
        localStorage.setItem('waaman_user', JSON.stringify(this.currentUser));
        
        // Dispatch profile update event
        document.dispatchEvent(new CustomEvent('auth:profile-updated', {
          detail: { user: this.currentUser }
        }));
        
        return { success: true, user: this.currentUser };
      }
      
      return { success: false, error: response.error };
    } catch (error) {
      console.error('Profile update error:', error);
      return { 
        success: false, 
        error: error.message || 'Profile update failed. Please try again.' 
      };
    }
  }

  async changePassword(currentPassword, newPassword) {
    try {
      const response = await this.apiService.put('/auth/change-password', {
        currentPassword,
        newPassword
      });

      if (response.success) {
        return { success: true, message: response.message };
      }
      
      return { success: false, error: response.error };
    } catch (error) {
      console.error('Password change error:', error);
      return { 
        success: false, 
        error: error.message || 'Password change failed. Please try again.' 
      };
    }
  }

  isAuthenticated() {
    return !!this.token && !!this.currentUser;
  }

  hasRole(role) {
    return this.currentUser && this.currentUser.role === role;
  }

  hasAnyRole(roles) {
    return this.currentUser && roles.includes(this.currentUser.role);
  }

  getToken() {
    return this.token;
  }

  getUserRole() {
    return this.currentUser ? this.currentUser.role : null;
  }

  getUserId() {
    return this.currentUser ? this.currentUser.id : null;
  }

  getUserName() {
    if (!this.currentUser) return null;
    return `${this.currentUser.firstName} ${this.currentUser.lastName}`;
  }

  // Check if token is expired (basic check)
  isTokenExpired() {
    if (!this.token) return true;
    
    try {
      const payload = JSON.parse(atob(this.token.split('.')[1]));
      const currentTime = Date.now() / 1000;
      return payload.exp < currentTime;
    } catch (error) {
      return true;
    }
  }

  // Refresh token if needed
  async refreshTokenIfNeeded() {
    if (this.isTokenExpired()) {
      await this.logout();
      return false;
    }
    return true;
  }
}
