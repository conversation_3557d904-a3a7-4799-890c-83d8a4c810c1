const admin = require('firebase-admin');

let db = null;

const initializeFirebase = () => {
  try {
    // Check if Firebase credentials are provided
    if (!process.env.FIREBASE_PROJECT_ID || !process.env.FIREBASE_PRIVATE_KEY || !process.env.FIREBASE_CLIENT_EMAIL) {
      console.warn('⚠️  Firebase credentials not provided. Running in development mode without Firebase.');
      return null;
    }

    // Check if Firebase is already initialized
    if (admin.apps.length === 0) {
      // Validate private key format
      let privateKey = process.env.FIREBASE_PRIVATE_KEY;
      if (!privateKey.includes('BEGIN PRIVATE KEY')) {
        console.warn('⚠️  Invalid Firebase private key format. Running without Firebase.');
        return null;
      }

      const serviceAccount = {
        type: "service_account",
        project_id: process.env.FIREBASE_PROJECT_ID,
        private_key: privateKey.replace(/\\n/g, '\n'),
        client_email: process.env.FIREBASE_CLIENT_EMAIL,
      };

      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        databaseURL: process.env.FIREBASE_DATABASE_URL,
        storageBucket: process.env.FIREBASE_STORAGE_BUCKET
      });

      console.log('✅ Firebase initialized successfully');
    }

    // Initialize Realtime Database
    db = admin.database();

    return db;
  } catch (error) {
    console.error('❌ Firebase initialization error:', error);
    console.warn('⚠️  Continuing without Firebase. Some features may not work.');
    return null;
  }
};

const getDatabase = () => {
  if (!db) {
    console.warn('⚠️  Firebase database not available. Using mock database.');
    return null;
  }
  return db;
};

const getAuth = () => {
  return admin.auth();
};

const getStorage = () => {
  return admin.storage();
};

// Database helper functions
const dbHelpers = {
  // Create a new document
  async create(path, data) {
    if (!db) {
      console.warn('⚠️  Firebase not available. Using mock data.');
      const mockId = Date.now().toString();
      return mockId;
    }
    const ref = db.ref(path);
    const newRef = ref.push();
    await newRef.set({
      ...data,
      id: newRef.key,
      createdAt: admin.database.ServerValue.TIMESTAMP,
      updatedAt: admin.database.ServerValue.TIMESTAMP
    });
    return newRef.key;
  },

  // Get a document by ID
  async getById(path, id) {
    if (!db) {
      console.warn('⚠️  Firebase not available. Returning mock data.');
      return null;
    }
    const snapshot = await db.ref(`${path}/${id}`).once('value');
    return snapshot.val();
  },

  // Get all documents in a path
  async getAll(path, orderBy = null, limit = null) {
    if (!db) {
      console.warn('⚠️  Firebase not available. Returning empty array.');
      return [];
    }
    let query = db.ref(path);

    if (orderBy) {
      query = query.orderByChild(orderBy);
    }

    if (limit) {
      query = query.limitToLast(limit);
    }

    const snapshot = await query.once('value');
    const data = snapshot.val();

    if (!data) return [];

    return Object.keys(data).map(key => ({
      id: key,
      ...data[key]
    }));
  },

  // Update a document
  async update(path, id, data) {
    if (!db) {
      console.warn('⚠️  Firebase not available. Update ignored.');
      return;
    }
    await db.ref(`${path}/${id}`).update({
      ...data,
      updatedAt: admin.database.ServerValue.TIMESTAMP
    });
  },

  // Delete a document
  async delete(path, id) {
    if (!db) {
      console.warn('⚠️  Firebase not available. Delete ignored.');
      return;
    }
    await db.ref(`${path}/${id}`).remove();
  },

  // Query documents with conditions
  async query(path, field, operator, value) {
    if (!db) {
      console.warn('⚠️  Firebase not available. Returning empty array.');
      return [];
    }
    let query = db.ref(path);

    switch (operator) {
      case '==':
        query = query.orderByChild(field).equalTo(value);
        break;
      case '>':
        query = query.orderByChild(field).startAt(value + 1);
        break;
      case '<':
        query = query.orderByChild(field).endAt(value - 1);
        break;
      case '>=':
        query = query.orderByChild(field).startAt(value);
        break;
      case '<=':
        query = query.orderByChild(field).endAt(value);
        break;
      default:
        throw new Error(`Unsupported operator: ${operator}`);
    }

    const snapshot = await query.once('value');
    const data = snapshot.val();

    if (!data) return [];

    return Object.keys(data).map(key => ({
      id: key,
      ...data[key]
    }));
  }
};

module.exports = {
  initializeFirebase,
  getDatabase,
  getAuth,
  getStorage,
  dbHelpers,
  admin
};
