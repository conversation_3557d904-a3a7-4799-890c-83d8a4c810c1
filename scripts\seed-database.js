const { initializeFirebase, dbHelpers } = require('../server/config/firebase');
const bcrypt = require('bcryptjs');

// Sample data
const sampleUsers = [
  {
    email: '<EMAIL>',
    password: 'admin123',
    firstName: 'Admin',
    lastName: 'User',
    role: 'admin',
    phone: '+1234567890'
  },
  {
    email: '<EMAIL>',
    password: 'driver123',
    firstName: '<PERSON>',
    lastName: 'Driver',
    role: 'driver',
    phone: '+1234567891'
  },
  {
    email: '<EMAIL>',
    password: 'user123',
    firstName: '<PERSON>',
    lastName: 'Citizen',
    role: 'user',
    phone: '+1234567892'
  },
  {
    email: '<EMAIL>',
    password: 'worker123',
    firstName: '<PERSON>',
    lastName: 'Worker',
    role: 'worker',
    phone: '+1234567893'
  }
];

const sampleVehicles = [
  {
    plateNumber: 'WM-001',
    type: 'compactor',
    capacity: 15,
    model: 'Volvo FE',
    year: 2022,
    status: 'active',
    location: {
      latitude: 40.7128,
      longitude: -74.0060,
      speed: 0,
      heading: 0,
      lastUpdated: new Date().toISOString()
    }
  },
  {
    plateNumber: 'WM-002',
    type: 'truck',
    capacity: 10,
    model: 'Mercedes Atego',
    year: 2021,
    status: 'active',
    location: {
      latitude: 40.7589,
      longitude: -73.9851,
      speed: 0,
      heading: 0,
      lastUpdated: new Date().toISOString()
    }
  },
  {
    plateNumber: 'WM-003',
    type: 'pickup',
    capacity: 5,
    model: 'Ford Transit',
    year: 2023,
    status: 'maintenance',
    location: {
      latitude: 40.7282,
      longitude: -73.7949,
      speed: 0,
      heading: 0,
      lastUpdated: new Date().toISOString()
    }
  }
];

const sampleRoutes = [
  {
    name: 'Downtown Route',
    description: 'Covers downtown area including business district',
    waypoints: [
      { latitude: 40.7128, longitude: -74.0060 },
      { latitude: 40.7589, longitude: -73.9851 },
      { latitude: 40.7282, longitude: -73.7949 }
    ],
    estimatedDuration: 120,
    area: 'Downtown',
    status: 'active'
  },
  {
    name: 'Residential Route A',
    description: 'Residential areas in the north sector',
    waypoints: [
      { latitude: 40.7831, longitude: -73.9712 },
      { latitude: 40.7794, longitude: -73.9632 },
      { latitude: 40.7751, longitude: -73.9660 }
    ],
    estimatedDuration: 90,
    area: 'North Residential',
    status: 'active'
  }
];

const sampleComplaints = [
  {
    title: 'Missed Collection',
    description: 'Garbage was not collected on the scheduled day. Bins are overflowing.',
    category: 'missed_collection',
    priority: 'high',
    status: 'pending',
    location: {
      latitude: 40.7128,
      longitude: -74.0060,
      address: '123 Main St, New York, NY'
    },
    anonymous: false,
    timeline: [
      {
        action: 'created',
        timestamp: new Date().toISOString(),
        note: 'Complaint submitted by user'
      }
    ],
    comments: []
  },
  {
    title: 'Damaged Bin',
    description: 'The waste bin has a large crack and is leaking.',
    category: 'damaged_bin',
    priority: 'medium',
    status: 'in_progress',
    location: {
      latitude: 40.7589,
      longitude: -73.9851,
      address: '456 Oak Ave, New York, NY'
    },
    anonymous: false,
    timeline: [
      {
        action: 'created',
        timestamp: new Date(Date.now() - 86400000).toISOString(),
        note: 'Complaint submitted by user'
      },
      {
        action: 'status_updated',
        status: 'in_progress',
        timestamp: new Date().toISOString(),
        note: 'Assigned to maintenance team'
      }
    ],
    comments: [
      {
        id: '1',
        comment: 'We have received your complaint and assigned it to our maintenance team.',
        userId: 'admin_user',
        userName: 'Admin User',
        userRole: 'admin',
        timestamp: new Date().toISOString()
      }
    ]
  }
];

async function seedDatabase() {
  try {
    console.log('🌱 Starting database seeding...');
    
    // Initialize Firebase
    const db = initializeFirebase();
    if (!db) {
      console.error('❌ Firebase not initialized. Please check your credentials.');
      return;
    }

    // Create users
    console.log('👥 Creating sample users...');
    const userIds = {};
    for (const userData of sampleUsers) {
      const hashedPassword = await bcrypt.hash(userData.password, 12);
      const userId = await dbHelpers.create('users', {
        ...userData,
        password: hashedPassword,
        status: 'active',
        profile: {
          avatar: null,
          preferences: {
            notifications: {
              email: true,
              push: true,
              sms: false
            }
          }
        }
      });
      userIds[userData.role] = userId;
      console.log(`✅ Created ${userData.role}: ${userData.email}`);
    }

    // Create routes
    console.log('🗺️  Creating sample routes...');
    const routeIds = [];
    for (const routeData of sampleRoutes) {
      const routeId = await dbHelpers.create('routes', routeData);
      routeIds.push(routeId);
      console.log(`✅ Created route: ${routeData.name}`);
    }

    // Create vehicles and assign to drivers
    console.log('🚛 Creating sample vehicles...');
    const vehicleIds = [];
    for (let i = 0; i < sampleVehicles.length; i++) {
      const vehicleData = {
        ...sampleVehicles[i],
        assignedTo: i === 0 ? userIds.driver : null,
        route: i < routeIds.length ? routeIds[i] : null,
        maintenance: {
          lastService: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
          nextService: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toISOString(),
          mileage: 45000 + (i * 5000)
        },
        fuel: {
          level: 75 + (i * 5),
          lastRefill: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          consumption: []
        }
      };
      const vehicleId = await dbHelpers.create('vehicles', vehicleData);
      vehicleIds.push(vehicleId);
      console.log(`✅ Created vehicle: ${vehicleData.plateNumber}`);
    }

    // Create complaints
    console.log('📝 Creating sample complaints...');
    for (let i = 0; i < sampleComplaints.length; i++) {
      const complaintData = {
        ...sampleComplaints[i],
        userId: userIds.user,
        userInfo: {
          name: 'Jane Citizen',
          email: '<EMAIL>',
          phone: '+1234567892'
        },
        assignedTo: i === 1 ? userIds.worker : null
      };
      const complaintId = await dbHelpers.create('complaints', complaintData);
      console.log(`✅ Created complaint: ${complaintData.title}`);
    }

    // Create some location history
    console.log('📍 Creating location history...');
    for (const vehicleId of vehicleIds) {
      for (let i = 0; i < 5; i++) {
        await dbHelpers.create('location_history', {
          vehicleId,
          latitude: 40.7128 + (Math.random() - 0.5) * 0.1,
          longitude: -74.0060 + (Math.random() - 0.5) * 0.1,
          speed: Math.floor(Math.random() * 50),
          heading: Math.floor(Math.random() * 360),
          timestamp: new Date(Date.now() - (i * 10 * 60 * 1000)).toISOString(),
          updatedBy: userIds.driver
        });
      }
    }

    console.log('🎉 Database seeding completed successfully!');
    console.log('\n📊 Summary:');
    console.log(`- Users: ${sampleUsers.length}`);
    console.log(`- Vehicles: ${sampleVehicles.length}`);
    console.log(`- Routes: ${sampleRoutes.length}`);
    console.log(`- Complaints: ${sampleComplaints.length}`);
    console.log(`- Location History: ${vehicleIds.length * 5} records`);
    
    console.log('\n🔑 Test Accounts:');
    sampleUsers.forEach(user => {
      console.log(`- ${user.role}: ${user.email} / ${user.password}`);
    });

  } catch (error) {
    console.error('❌ Error seeding database:', error);
  }
}

// Run the seeding if this file is executed directly
if (require.main === module) {
  seedDatabase().then(() => {
    process.exit(0);
  }).catch((error) => {
    console.error('❌ Seeding failed:', error);
    process.exit(1);
  });
}

module.exports = { seedDatabase };
