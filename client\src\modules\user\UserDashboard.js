import { AuthService } from '../../services/auth.js';
import { ApiService } from '../../services/api.js';

export class UserDashboard {
  constructor() {
    this.app = document.getElementById('app');
    this.authService = new AuthService();
    this.apiService = new ApiService();
    this.user = null;
  }

  async render() {
    // Get current user asynchronously
    this.user = await this.authService.getCurrentUser();
    console.log('UserDashboard - Current user:', this.user);

    if (!this.user || !this.user.id) {
      console.log('No valid user found in UserDashboard, redirecting to login');
      window.location.href = '/login';
      return;
    }

    this.app.innerHTML = `
      <div class="min-h-screen bg-gray-50">
        <!-- Navigation -->
        <nav class="bg-white shadow-sm border-b border-gray-200">
          <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <h1 class="text-xl font-bold text-primary-600">
                    <i class="fas fa-recycle mr-2"></i>
                    WaaMan
                  </h1>
                </div>
                <div class="hidden md:ml-6 md:flex md:space-x-8">
                  <a href="#" class="nav-link-active">
                    <i class="fas fa-tachometer-alt mr-2"></i>
                    Dashboard
                  </a>
                  <button data-route="/tracking" class="nav-link-inactive">
                    <i class="fas fa-map-marker-alt mr-2"></i>
                    Track Vehicles
                  </button>
                  <button data-route="/complaints" class="nav-link-inactive">
                    <i class="fas fa-exclamation-circle mr-2"></i>
                    Complaints
                  </button>
                </div>
              </div>
              <div class="flex items-center space-x-4">
                <div class="relative">
                  <button class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500" id="user-menu-button">
                    <img class="h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center" src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236366f1'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z'/%3E%3C/svg%3E" alt="">
                    <span class="ml-2 text-gray-700">${this.user.firstName || 'User'} ${this.user.lastName || ''}</span>
                    <i class="fas fa-chevron-down ml-1 text-gray-400"></i>
                  </button>
                  <div id="user-menu" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                    <button data-route="/profile" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left">
                      <i class="fas fa-user mr-2"></i>
                      Profile
                    </button>
                    <button id="logout-btn" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left">
                      <i class="fas fa-sign-out-alt mr-2"></i>
                      Sign out
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </nav>

        <!-- Main Content -->
        <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <!-- Welcome Section -->
          <div class="px-4 py-6 sm:px-0">
            <div class="bg-white overflow-hidden shadow rounded-lg">
              <div class="px-4 py-5 sm:p-6">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <div class="h-12 w-12 bg-primary-100 rounded-full flex items-center justify-center">
                      <i class="fas fa-home text-primary-600 text-xl"></i>
                    </div>
                  </div>
                  <div class="ml-4">
                    <h2 class="text-lg font-medium text-gray-900">
                      Welcome back, ${this.user.firstName || 'User'}!
                    </h2>
                    <p class="text-sm text-gray-500">
                      Track waste collection vehicles and manage your complaints from your dashboard.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Quick Actions -->
          <div class="px-4 py-6 sm:px-0">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Quick Actions</h3>
            <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
              <!-- Track Vehicles -->
              <div class="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-primary-500 rounded-lg shadow hover:shadow-md transition-shadow">
                <div>
                  <span class="rounded-lg inline-flex p-3 bg-primary-50 text-primary-600 ring-4 ring-white">
                    <i class="fas fa-truck text-xl"></i>
                  </span>
                </div>
                <div class="mt-4">
                  <h3 class="text-lg font-medium">
                    <button data-route="/tracking" class="focus:outline-none">
                      <span class="absolute inset-0" aria-hidden="true"></span>
                      Track Vehicles
                    </button>
                  </h3>
                  <p class="mt-2 text-sm text-gray-500">
                    See real-time locations of waste collection vehicles in your area.
                  </p>
                </div>
                <span class="pointer-events-none absolute top-6 right-6 text-gray-300 group-hover:text-gray-400" aria-hidden="true">
                  <i class="fas fa-arrow-right"></i>
                </span>
              </div>

              <!-- Submit Complaint -->
              <div class="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-primary-500 rounded-lg shadow hover:shadow-md transition-shadow">
                <div>
                  <span class="rounded-lg inline-flex p-3 bg-warning-50 text-warning-600 ring-4 ring-white">
                    <i class="fas fa-exclamation-triangle text-xl"></i>
                  </span>
                </div>
                <div class="mt-4">
                  <h3 class="text-lg font-medium">
                    <button data-route="/complaints" class="focus:outline-none">
                      <span class="absolute inset-0" aria-hidden="true"></span>
                      Submit Complaint
                    </button>
                  </h3>
                  <p class="mt-2 text-sm text-gray-500">
                    Report issues with waste collection or submit feedback.
                  </p>
                </div>
                <span class="pointer-events-none absolute top-6 right-6 text-gray-300 group-hover:text-gray-400" aria-hidden="true">
                  <i class="fas fa-arrow-right"></i>
                </span>
              </div>

              <!-- View Profile -->
              <div class="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-primary-500 rounded-lg shadow hover:shadow-md transition-shadow">
                <div>
                  <span class="rounded-lg inline-flex p-3 bg-success-50 text-success-600 ring-4 ring-white">
                    <i class="fas fa-user text-xl"></i>
                  </span>
                </div>
                <div class="mt-4">
                  <h3 class="text-lg font-medium">
                    <button data-route="/profile" class="focus:outline-none">
                      <span class="absolute inset-0" aria-hidden="true"></span>
                      Manage Profile
                    </button>
                  </h3>
                  <p class="mt-2 text-sm text-gray-500">
                    Update your personal information and notification preferences.
                  </p>
                </div>
                <span class="pointer-events-none absolute top-6 right-6 text-gray-300 group-hover:text-gray-400" aria-hidden="true">
                  <i class="fas fa-arrow-right"></i>
                </span>
              </div>
            </div>
          </div>

          <!-- Recent Activity -->
          <div class="px-4 py-6 sm:px-0">
            <div class="bg-white shadow rounded-lg">
              <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Recent Activity</h3>
                <div id="recent-activity" class="space-y-4">
                  <div class="flex items-center justify-center py-8 text-gray-500">
                    <div class="text-center">
                      <i class="fas fa-clock text-3xl mb-2"></i>
                      <p>Loading recent activity...</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    `;

    this.attachEventListeners();
    this.loadRecentActivity();
  }

  attachEventListeners() {
    // User menu toggle
    const userMenuButton = document.getElementById('user-menu-button');
    const userMenu = document.getElementById('user-menu');
    
    userMenuButton.addEventListener('click', () => {
      userMenu.classList.toggle('hidden');
    });

    // Close menu when clicking outside
    document.addEventListener('click', (event) => {
      if (!userMenuButton.contains(event.target) && !userMenu.contains(event.target)) {
        userMenu.classList.add('hidden');
      }
    });

    // Logout button
    const logoutBtn = document.getElementById('logout-btn');
    logoutBtn.addEventListener('click', async () => {
      await this.authService.logout();
    });
  }

  async loadRecentActivity() {
    const activityContainer = document.getElementById('recent-activity');
    
    try {
      // This would typically load user's recent complaints, notifications, etc.
      // For now, we'll show a placeholder
      activityContainer.innerHTML = `
        <div class="text-center py-8 text-gray-500">
          <i class="fas fa-inbox text-3xl mb-2"></i>
          <p>No recent activity</p>
          <p class="text-sm">Your complaints and notifications will appear here.</p>
        </div>
      `;
    } catch (error) {
      console.error('Error loading recent activity:', error);
      activityContainer.innerHTML = `
        <div class="text-center py-8 text-red-500">
          <i class="fas fa-exclamation-triangle text-3xl mb-2"></i>
          <p>Failed to load recent activity</p>
        </div>
      `;
    }
  }
}
