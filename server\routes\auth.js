const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { dbHelpers } = require('../config/firebase');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// Register new user
router.post('/register', async (req, res) => {
  try {
    const { email, password, firstName, lastName, role = 'user', phone } = req.body;

    console.log('Registration attempt:', { email, firstName, lastName, role });

    // Validation
    if (!email || !password || !firstName || !lastName) {
      return res.status(400).json({
        success: false,
        error: 'All required fields must be provided'
      });
    }

    // Check if user already exists (skip if Firebase not available)
    try {
      const existingUsers = await dbHelpers.query('users', 'email', '==', email);
      if (existingUsers.length > 0) {
        return res.status(400).json({
          success: false,
          error: 'User with this email already exists'
        });
      }
    } catch (error) {
      console.warn('Database query failed, continuing with mock mode');
    }

    // Hash password
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // Create user data
    const userData = {
      email,
      password: hashedPassword,
      firstName,
      lastName,
      role,
      phone: phone || null,
      status: 'active',
      profile: {
        avatar: null,
        preferences: {
          notifications: {
            email: true,
            push: true,
            sms: false
          }
        }
      }
    };

    // Save user to database (or create mock user if Firebase not available)
    let userId;
    try {
      userId = await dbHelpers.create('users', userData);
    } catch (error) {
      console.warn('Database create failed, using mock user ID');
      userId = `mock_${Date.now()}`;
    }

    // Generate JWT token
    const token = jwt.sign(
      { userId, email, role },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
    );

    // Remove password from response
    delete userData.password;

    console.log('Registration successful for:', email, 'with role:', role);

    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      data: {
        user: { id: userId, ...userData },
        token
      }
    });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({
      success: false,
      error: 'Registration failed'
    });
  }
});

// Login user
router.post('/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    console.log('Login attempt:', email);

    // Validation
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        error: 'Email and password are required'
      });
    }

    // Find user by email (or use mock data if Firebase not available)
    let users = [];

    try {
      users = await dbHelpers.query('users', 'email', '==', email);
    } catch (error) {
      console.warn('Database query failed, using mock authentication');

      // Mock users for testing - check credentials directly
      const mockCredentials = {
        '<EMAIL>': { password: 'admin123', role: 'admin', firstName: 'Admin', lastName: 'User' },
        '<EMAIL>': { password: 'driver123', role: 'driver', firstName: 'John', lastName: 'Driver' },
        '<EMAIL>': { password: 'user123', role: 'user', firstName: 'Jane', lastName: 'Citizen' }
      };

      if (mockCredentials[email] && mockCredentials[email].password === password) {
        const mockUser = {
          id: `mock_${mockCredentials[email].role}`,
          email: email,
          firstName: mockCredentials[email].firstName,
          lastName: mockCredentials[email].lastName,
          role: mockCredentials[email].role,
          status: 'active',
          password: await bcrypt.hash(password, 12) // Hash for consistency
        };
        users = [mockUser];
      }
    }

    if (users.length === 0) {
      return res.status(401).json({
        success: false,
        error: 'Invalid credentials'
      });
    }

    const user = users[0];

    // Check if user is active
    if (user.status !== 'active') {
      return res.status(401).json({
        success: false,
        error: 'Account is not active'
      });
    }

    // Verify password (skip bcrypt for mock users as password is already verified)
    let isPasswordValid = false;
    if (user.id && user.id.startsWith('mock_')) {
      // For mock users, password was already verified above
      isPasswordValid = true;
    } else {
      // For real users, use bcrypt
      isPasswordValid = await bcrypt.compare(password, user.password);
    }

    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        error: 'Invalid credentials'
      });
    }

    // Generate JWT token
    const token = jwt.sign(
      { userId: user.id, email: user.email, role: user.role },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
    );

    // Update last login (skip if Firebase not available)
    try {
      await dbHelpers.update('users', user.id, {
        lastLogin: new Date().toISOString()
      });
    } catch (error) {
      console.warn('Database update failed, continuing with mock mode');
    }

    // Remove password from response
    delete user.password;

    console.log('Login successful for:', email, 'with role:', user.role);

    res.json({
      success: true,
      message: 'Login successful',
      data: {
        user,
        token
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      error: 'Login failed'
    });
  }
});

// Get current user profile
router.get('/profile', authenticateToken, async (req, res) => {
  try {
    // User is already attached to req by authenticateToken middleware
    const user = { ...req.user };
    delete user.password; // Ensure password is not sent

    res.json({
      success: true,
      data: { user }
    });
  } catch (error) {
    console.error('Profile fetch error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch profile'
    });
  }
});

// Update user profile
router.put('/profile', authenticateToken, async (req, res) => {
  try {
    const { firstName, lastName, phone, profile } = req.body;
    const userId = req.user.id;

    const updateData = {};
    if (firstName) updateData.firstName = firstName;
    if (lastName) updateData.lastName = lastName;
    if (phone) updateData.phone = phone;
    if (profile) updateData.profile = { ...req.user.profile, ...profile };

    await dbHelpers.update('users', userId, updateData);

    // Get updated user
    const updatedUser = await dbHelpers.getById('users', userId);
    delete updatedUser.password;

    res.json({
      success: true,
      message: 'Profile updated successfully',
      data: { user: updatedUser }
    });
  } catch (error) {
    console.error('Profile update error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update profile'
    });
  }
});

// Change password
router.put('/change-password', authenticateToken, async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;
    const userId = req.user.id;

    if (!currentPassword || !newPassword) {
      return res.status(400).json({
        success: false,
        error: 'Current password and new password are required'
      });
    }

    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, req.user.password);
    if (!isCurrentPasswordValid) {
      return res.status(400).json({
        success: false,
        error: 'Current password is incorrect'
      });
    }

    // Hash new password
    const saltRounds = 12;
    const hashedNewPassword = await bcrypt.hash(newPassword, saltRounds);

    // Update password
    await dbHelpers.update('users', userId, {
      password: hashedNewPassword
    });

    res.json({
      success: true,
      message: 'Password changed successfully'
    });
  } catch (error) {
    console.error('Password change error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to change password'
    });
  }
});

// Logout (client-side token removal, but we can log it)
router.post('/logout', authenticateToken, async (req, res) => {
  try {
    // Update last logout time
    await dbHelpers.update('users', req.user.id, {
      lastLogout: new Date().toISOString()
    });

    res.json({
      success: true,
      message: 'Logged out successfully'
    });
  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({
      success: false,
      error: 'Logout failed'
    });
  }
});

module.exports = router;
